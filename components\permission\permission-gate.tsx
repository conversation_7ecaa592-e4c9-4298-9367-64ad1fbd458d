/**
 * Permission Gate Component
 * Conditionally renders children based on user permissions
 */

import React from 'react'
import { useUserPermissions, PermissionCodename } from '@/hooks/use-user-permissions'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ShieldX } from 'lucide-react'

interface PermissionGateProps {
  /** Permission codename required to access the content */
  permission: PermissionCodename | string
  /** Children to render when permission is granted */
  children: React.ReactNode
  /** Custom fallback component when permission is denied (optional) */
  fallback?: React.ReactNode
  /** Whether to show loading state */
  showLoading?: boolean
  /** Whether to show access denied message */
  showAccessDenied?: boolean
}

export function PermissionGate({ 
  permission, 
  children, 
  fallback,
  showLoading = true,
  showAccessDenied = true 
}: PermissionGateProps) {
  const { hasPermission, loading, error } = useUserPermissions()
  
  if (loading && showLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
        <span className="ml-2 text-sm text-muted-foreground">Checking permissions...</span>
      </div>
    )
  }
  
  if (error) {
    return (
      <Alert variant="destructive">
        <ShieldX className="h-4 w-4" />
        <AlertDescription>
          Error loading permissions: {error}
        </AlertDescription>
      </Alert>
    )
  }
  
  const hasAccess = hasPermission(permission)
  
  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    if (showAccessDenied) {
      return (
        <Alert>
          <ShieldX className="h-4 w-4" />
          <AlertDescription>
            You don't have permission to access this content.
          </AlertDescription>
        </Alert>
      )
    }
    
    return null
  }
  
  return <>{children}</>
}