# Permission-Based Access Control System

This document describes the comprehensive permission-based access control system implemented for the Grandvalira Newsletter application.

## Overview

The system restricts user actions based on permissions assigned through Django user groups. Permissions are fetched from the `/user/me` endpoint and cached for performance.

## Permission Codenames

The following permission codenames are defined and used throughout the application:

- `crear_i_editar_newsletters`: Allow creating and editing newsletters
- `veure_newsletters`: Allow viewing newsletters (read-only access)
- `veure_i_gestionar_blocs`: Allow viewing and managing newsletter blocks
- `veure_i_gestionar_capcaleres_i_peus`: Allow viewing and managing headers and footers
- `veure_i_gestionar_plantilles`: Allow viewing and managing templates
- `veure_i_gestionar_usuaris_i_rols`: Allow viewing and managing users and roles

## Core Components

### useUserPermissions Hook

Located in `/hooks/use-user-permissions.ts`, this hook:
- Fetches user data from `/user/me` endpoint via `useMe` hook
- Extracts permissions from user groups and direct permissions
- Provides `hasPermission(codename)` function for permission checking
- Handles loading states and error cases

```typescript
import { useUserPermissions, PERMISSION_CODENAMES } from '@/hooks/use-user-permissions'

function MyComponent() {
  const { hasPermission, loading } = useUserPermissions()
  const canEdit = hasPermission(PERMISSION_CODENAMES.CREATE_EDIT_NEWSLETTERS)
  
  return (
    <div>
      {canEdit && <button>Edit Newsletter</button>}
    </div>
  )
}
```

### PermissionGate Component

Located in `/components/permission/permission-gate.tsx`, this component:
- Conditionally renders children based on user permissions
- Shows loading states and access denied messages
- Supports custom fallback components

```typescript
import { PermissionGate, PERMISSION_CODENAMES } from '@/components/permission'

<PermissionGate permission={PERMISSION_CODENAMES.VIEW_NEWSLETTERS}>
  <NewsletterList />
</PermissionGate>
```

### PermissionButton Component

Located in `/components/permission/permission-button.tsx`, this component:
- Extends the Button component with permission checking
- Can be disabled or hidden based on permissions
- Provides tooltips for disabled states

```typescript
import { PermissionButton, PERMISSION_CODENAMES } from '@/components/permission'

<PermissionButton
  permission={PERMISSION_CODENAMES.CREATE_EDIT_NEWSLETTERS}
  onClick={handleSave}
  hideWhenDenied={true}
>
  Save Newsletter
</PermissionButton>
```

## Applied Restrictions

### Newsletter Management
- **AI Dialog** (`/components/newsletter/ai-dialog.tsx`): Requires `crear_i_editar_newsletters`
- **Newsletter Builder** (`/app/newsletter/builder/[id]/page.tsx`): 
  - Global AI button requires `crear_i_editar_newsletters`
  - Save button requires `crear_i_editar_newsletters`
- **Block Accordion** (`/components/newsletter/block-accordion.tsx`):
  - AI generation buttons require `crear_i_editar_newsletters`
  - Delete buttons require `crear_i_editar_newsletters`

### Page-Level Restrictions
- **Newsletter List** (`/app/page.tsx`): Requires `veure_newsletters`
- **Templates** (`/app/templates/page.tsx`): Requires `veure_i_gestionar_plantilles`
- **Blocks** (`/app/blocks/page.tsx`): Requires `veure_i_gestionar_blocs`
- **Block Management** (`/app/blocks/block/page.tsx`): Requires `veure_i_gestionar_blocs`
- **Headers/Footers** (`/app/blocks/header-footer/page.tsx`): Requires `veure_i_gestionar_capcaleres_i_peus`
- **User Management** (`/app/configuration/users/page.tsx`): Requires `veure_i_gestionar_usuaris_i_rols`

## User Data Structure

The permission system expects user data from `/user/me` endpoint in the following format:

```json
{
  "user": {
    "user": {
      "id": "uuid",
      "groups": [
        {
          "id": 4,
          "name": "Role Name",
          "permissions": [
            {
              "id": 110,
              "name": "Permission Display Name",
              "codename": "permission_codename",
              "content_type": 8
            }
          ]
        }
      ],
      "user_permissions": []
    }
  }
}
```

## Implementation Details

### Permission Extraction
The system combines permissions from:
1. User groups (`user.groups[].permissions`)
2. Direct user permissions (`user.user_permissions`)

Permissions are deduplicated by ID to avoid conflicts.

### Caching Strategy
- Permissions are cached in the component state via `useMe` hook
- User store provides additional caching layer
- Permissions are refetched when user data changes

### Error Handling
- Loading states are handled gracefully
- Network errors show appropriate error messages
- Missing permissions result in disabled/hidden UI elements

## Best Practices

1. **Use PermissionGate for entire sections**: Wrap full page content or major sections
2. **Use PermissionButton for individual actions**: Apply to specific buttons that perform actions
3. **Combine read and write permissions**: Use `veure_newsletters` for viewing and `crear_i_editar_newsletters` for editing
4. **Provide meaningful error messages**: Use custom tooltips and fallback content
5. **Test with different permission sets**: Ensure UI degrades gracefully

## Testing

Test the permission system by:
1. Creating users with different permission sets in Django admin
2. Logging in with different users
3. Verifying that UI elements are properly hidden/disabled
4. Checking that API calls fail appropriately for unauthorized actions

## Migration from Existing System

The new system replaces the old `/users/permissions/` endpoint approach with user group-based permissions from `/user/me`. This provides:
- Better performance (single endpoint call)
- More accurate permission representation
- Support for multiple roles per user
- Consistent with Django's permission model