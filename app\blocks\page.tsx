"use client";

import Link from "next/link";
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Cuboid, Database } from "lucide-react";
import { PermissionGate } from "@/components/permission/permission-gate";
import { PERMISSION_CODENAMES } from "@/hooks/use-user-permissions";

export default function BlocksPage() {
  const blocksSections = [
    {
      title: "Blocs",
      description: "",
      icon: Cuboid,
      href: "/blocks/block",
      color: "text-primary"
    },
    {
      title: "Capçaleres i peus de pàgina",
      description: "",
      icon: Database,
      href: "/blocks/header-footer",
      color: "text-green-600"
    },
  ];

  return (
    <PermissionGate permission={PERMISSION_CODENAMES.VIEW_MANAGE_BLOCKS}>
      <div className="p-6 space-y-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Configu<PERSON><PERSON><PERSON></h1>
          <p className="text-muted-foreground">
            Gestiona els teus blocs de contingut, capçaleres i peus de pàgina.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
        {blocksSections.map((section) => {
          const IconComponent = section.icon;
          return (
            <Link key={section.title} href={section.href}>
              <Card className="h-full transition-colors hover:bg-muted/50">
                <CardHeader className="flex flex-row items-center space-y-0 pb-2">
                  <div className="flex items-center space-x-2">
                    <IconComponent className={`h-6 w-6 ${section.color}`} />
                    <CardTitle className="text-lg">{section.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-sm">
                    {section.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </Link>
          );
        })}
      </div>
      </div>
    </PermissionGate>
  );
}
